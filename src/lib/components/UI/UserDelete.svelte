<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Alert } from 'flowbite-svelte';
	import { toastStore } from '$lib/stores/toastStore';
	import { t } from '$src/lib/stores/i18n';

	export let user: any;

	let deleteForm: HTMLFormElement;
	let deleteModalOpen = false;
	let selectInstances: any = null;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support
	let validationError = ''; // Username validation error

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when input changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
		validationError = ''; // Clear validation error
	}

	// Interface for enhance options
	interface EnhanceOptions {
		modalOpen: boolean;
		setModalOpen?: (value: boolean) => void;
		setPending?: (value: boolean) => void;
		setShowSuccessMessage?: (value: boolean) => void;
		setSuccessMessage?: (value: string) => void;
		setShowErrorMessage?: (value: boolean) => void;
		setErrorMessage?: (value: string) => void;
		// New optional properties for enhanced behavior
		useToastOnSuccess?: boolean;
		closeModalOnSuccess?: boolean;
	}

	// Local implementation of handleEnhance function
	function handleEnhance(options: EnhanceOptions) {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			// Set pending state if the option is provided
			options.setPending?.(true);

			if (result.type === 'failure') {
				options.setErrorMessage?.(result.data?.error || 'Status : Operation failed');
				// Don't close modal on error
			} else if (result.type === 'success') {
				const successMessage =
					t(user.is_active ? 'user_deactivate_success' : 'user_reactivate_success') ||
					'Status : Operation success';

				if (options.useToastOnSuccess) {
					toastStore.add(successMessage, 'success');
				} else {
					// Existing behavior for backward compatibility
					options.setShowSuccessMessage?.(true);
					options.setSuccessMessage?.(successMessage);
				}

				if (options.closeModalOnSuccess) {
					options.setModalOpen?.(false);
				}
			}
			// Update the page data
			await update();

			// Reset pending state if the option is provided
			options.setPending?.(false);
		};
	}

	// Reactive statement for username validation
	$: isValidUsername = formData.confirm_username.trim() === user.username;

	function openDeleteModal(user: any) {
		selectInstances = { ...user };
		deleteModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
		validationError = ''; // Reset validation error
		formData.confirm_username = ''; // Reset form data
	}

	function handleDeleteSubmit(event: Event) {
		// TODO - Delete this
		// console.log(`handleDeleteSubmit's formData.username  - ${formData.confirm_username}`);
		// console.log(`handleDeleteSubmit's user.username  - ${user.username}`);
		// console.log(`handleDeleteSubmit: ${JSON.stringify(formData)}`);

		// Reset messages
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';

		// Validate the username matches
		if (!isValidUsername) {
			event.preventDefault();
			validationError = t('user_deactivate_validation_error');
			return;
		}

		// If user is being reactivated, update the form data
		if (user.is_active === false) {
			// Update the form data to reflect reactivation
			formData.is_active = true;
		}

		// // Validate the filename matches
		// if (formData.username !== user.username) {
		//     event.preventDefault();
		//     showErrorMessage = true;
		//     errorMessage = 'Username does not match';
		//     return;
		// }
	}

	// TODO - Delete this if the new one is work
	// $: enhanceOptions = {
	//     modalOpen: deleteModalOpen,
	//     setModalOpen: (value: boolean) => deleteModalOpen = value,
	//     setSuccessMessage: (value: boolean) => showSuccessMessage = value,
	//     setErrorMessage: (value: boolean) => showErrorMessage = value,
	//     setErrorText: (value: string) => errorMessage = value
	// };

	$: enhanceOptions = {
		modalOpen: deleteModalOpen,
		setModalOpen: (value: boolean) => (deleteModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - close modal and show toast
		useToastOnSuccess: true,
		closeModalOnSuccess: true
	};

	// Form data with initial values from user
	let formData = {
		name: user.name,
		first_name: user.first_name,
		last_name: user.last_name,
		work_email: user.work_email,
		is_active: user.is_active,
		confirm_username: ''
	}; // Not to auto-fill the username when opening the modal
	// let formData = {
	// 	confirm_username: user.username
	// };

	// TODO - Delete this
	// onst roles = ['Admin', 'Supervisor', 'Agent']; // Update based on your available roles
</script>

<Button
	color="none"
	class="w-full justify-start p-0 text-left text-red-600 hover:bg-gray-100"
	on:click={() => openDeleteModal(user)}
>
	{#if user.is_active}
		{t('user_deactivate_user')}
	{:else}
		{t('user_reactivate_user')}
	{/if}
</Button>

<Modal
	bind:open={deleteModalOpen}
	size="md"
	title={user.is_active ? t('user_deactivate_user') : t('user_reactivate_user')}
	on:close={() => {
		// Reset form data when modal is closed via X button or other means
		formData.confirm_username = '';
		validationError = '';
		fieldErrors = {};
	}}
>
	<h2 slot="header" class="text-red-600">
		{user.is_active ? t('user_deactivate_user') : t('user_reactivate_user')}
	</h2>
	{#if selectInstances}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}
		<!-- Validation error display -->
		{#if validationError}
			<Alert color="red" class="mb-4">
				{validationError}
			</Alert>
		{/if}
		<form
			bind:this={deleteForm}
			action={user.is_active ? '?/delete_user' : '?/update_user'}
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleDeleteSubmit}
		>
			<input type="hidden" name="id" value={user.id} />
			<input type="hidden" name="name" value={user.name} />
			<input type="hidden" name="employee_id" value={user.employee_id} />
			<input type="hidden" name="first_name" value={user.first_name} />
			<input type="hidden" name="last_name" value={user.last_name} />
			<input type="hidden" name="work_email" value={user.work_email} />
			<input type="hidden" name="is_active" value={user.is_active} />
			<input type="hidden" name="username" value={user.username} />
			<div>
				<Label for="username" class="mb-2 space-y-2 text-left">
					{user.is_active
						? t('user_deactivate_instructions_p1')
						: t('user_reactivate_instructions_p1')}
					"{user.username}"
					{user.is_active
						? t('user_deactivate_instructions_p2')
						: t('user_reactivate_instructions_p2')}
				</Label>
				<Input
					id="confirm_username"
					name="confirm_username"
					type="text"
					placeholder={t('user_deactivate_placeholder')}
					bind:value={formData.confirm_username}
					on:input={dismissAlerts}
					on:keydown={(event) => {
						if (event.key === 'Enter') {
							if (!isValidUsername) {
								event.preventDefault();
								validationError = t('user_deactivate_validation_error');
							}
							// If validation passes, allow normal form submission
						}
					}}
					required
				/>
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button
			type="submit"
			color="red"
			disabled={!isValidUsername}
			on:click={() => deleteForm.requestSubmit()}
			>{user.is_active ? t('user_deactivate_button') : t('user_reactivate_button')}</Button
		>
		<Button
			color="alternative"
			on:click={() => {
				deleteModalOpen = false;
				// Reset form data when modal is closed via Cancel button
				formData.confirm_username = '';
				validationError = '';
				fieldErrors = {};
			}}>{t('cancel')}</Button
		>
	</svelte:fragment>
</Modal>
