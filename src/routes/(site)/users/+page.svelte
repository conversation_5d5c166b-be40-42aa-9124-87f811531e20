<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';

	import {
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Table,
		Button,
		Dropdown,
		Checkbox,
		Spinner,
		Indicator,
		Avatar,
		Badge,
		Input,
		Tooltip
	} from 'flowbite-svelte';
	import {
		EditSolid,
		ChevronDownOutline,
		UserHeadsetSolid,
		AdjustmentsHorizontalSolid,
		UserCircleSolid,
		SearchOutline,
		ClockSolid
	} from 'flowbite-svelte-icons';
	import { getColorClass } from '$lib/utils';

	import { CaretDownSolid, CaretUpSolid } from 'flowbite-svelte-icons';
	import { formatTimestamp, displayDate, timeAgo } from '$lib/utils';
	import type { PageData } from './$types';
	import UserSignUp from '$lib/components/UI/UserSignUp.svelte';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
	import AdminStatistics from '$src/lib/components/admin/AdminStatistics.svelte';
	export let data: PageData;

	$: ({ users, statuses, roles, partners, error, all_tags } = data);

	// Current role
	// console.log("data.role = " + data.role);

	interface User {
		id: number;
		roles: string;
		partners: string;
		username: string;
		email: string;
		status: string;
		is_active: boolean;
		name: string;
		employee_id: number;
		current_workload: number;
		last_active: string;
	}

	type FilterState = {
		status: Set<string>;
		role: Set<string>;
		partner: Set<string>;
		activeStatus: string;
	};

	// Initialize filter states
	const filterState: FilterState = {
		status: new Set(['All']),
		role: new Set(['All']),
		partner: new Set(['All']),
		activeStatus: 'All'
	};

	// Reactive declarations for selected filters
	$: selectedStatuses = filterState.status;
	$: selectedRoles = filterState.role;
	$: selectedPartners = filterState.partner;
	$: selectedActiveStatus = filterState.activeStatus;

	// Available options with proper typing
	const statusOptions = ['All', 'online', 'busy', 'away', 'offline'];

	// Extract unique roles and partners from users
	$: roleOptions = [
		'All',
		...Array.from(
			new Set(
				users
					?.flatMap((user) => user.roles.split(',').map((role) => role.trim()))
					.filter((role) => role !== '') || []
			)
		)
	];

	$: partnerOptions = [
		'All',
		...Array.from(
			new Set(
				partners
					.map((partner) => partner.name)
					.filter((partner) => partner !== '' && partner.toLowerCase() !== 'all')
			)
		).sort((a, b) => a.localeCompare(b))
	];

	$: partnerCounts = {};

	$: {
		partnerCounts = {};
		for (const user of users) {
			for (const partner of user.partners) {
				const name = partner.name;
				if (name !== '' && name !== 'all') {
					partnerCounts[name] = (partnerCounts[name] || 0) + 1;
				}
			}
		}
	}

	const activeStatusOptions = ['All', 'Active', 'Inactive'];

	// Helper function to check if arrays have common elements
	function hasCommonElement(selected: Set<string>, userItems: string): boolean {
		if (selected.has('All')) return true;
		if (!userItems) return false;

		const userItemsArray = userItems.split(',').map((item) => item.trim());
		return Array.from(selected).some(
			(selectedItem) => selectedItem !== 'All' && userItemsArray.includes(selectedItem)
		);
	}

	function isPartnersContained(
		partners: { name: string; code: string }[],
		searchSet: Set<string>
	): boolean {
		const loweredSet = new Set(Array.from(searchSet).map((s) => s.toLowerCase()));

		return partners.some((partner) => loweredSet.has(partner.name.toLowerCase()));
	}

	// Improved filter function
	$: filteredUsers = users?.filter((user) => {
		currentPage = 1;
		const statusMatch = selectedStatuses.has('All') || selectedStatuses.has(user.status);
		const roleMatch = hasCommonElement(selectedRoles, user.roles);
		const partnerMatch =
			selectedPartners.has('All') || isPartnersContained(user.partners, selectedPartners);

		const activeMatch =
			selectedActiveStatus === 'All' ||
			(selectedActiveStatus === 'Active' && user.is_active) ||
			(selectedActiveStatus === 'Inactive' && !user.is_active);

		const searchMatch =
			String(user.id).toLowerCase().includes(searchQuery.toLowerCase()) ||
			user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			user.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			user.last_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			user.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
			user.roles.toLowerCase().includes(searchQuery.toLowerCase());

		return statusMatch && roleMatch && partnerMatch && activeMatch && searchMatch;
	});

	function toggleFilter(
		value: string,
		selectedSet: Set<string>,
		filterType: keyof Omit<FilterState, 'activeStatus'>
	) {
		const newSet = new Set(selectedSet);

		if (value === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(value)) {
				newSet.delete(value);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(value);
			}
		}

		filterState[filterType] = newSet;
		return newSet;
	}

	const toggleStatus = (status: string) =>
		(selectedStatuses = toggleFilter(status, selectedStatuses, 'status'));
	const toggleRole = (role: string) => (selectedRoles = toggleFilter(role, selectedRoles, 'role'));
	const togglePartner = (partner: string) =>
		(selectedPartners = toggleFilter(partner, selectedPartners, 'partner'));

	function resetFilters() {
		filterState.status = new Set(['All']);
		filterState.role = new Set(['All']);
		filterState.partner = new Set(['All']);
		filterState.activeStatus = 'All';
		sortColumn = 'id';
		sortDirection = 'asc'; // Default to ascending order
	}
	// Declare searchQuery
	let searchQuery = '';

	// Reactive declaration for sorting the filtered users based on sortColumn and sortDirection
	$: sortedUsers = [...filteredUsers].sort((a, b) => {
		if (sortColumn === 'id') {
			return sortDirection === 'asc' ? a.id - b.id : b.id - a.id;
		} else if (sortColumn === 'name') {
			return sortDirection === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);
		} else if (sortColumn === 'status') {
			return sortDirection === 'asc'
				? a.status.localeCompare(b.status)
				: b.status.localeCompare(a.status);
		} else if (sortColumn === 'last_active') {
			const aTime = new Date(a.last_active).getTime();
			const bTime = new Date(b.last_active).getTime();
			return sortDirection === 'asc' ? aTime - bTime : bTime - aTime;
		}

		// Add more sorting logic for other columns if needed
		return 0;
	});

	// Sort state for each column
	let sortColumn = 'status'; // Default to sorting by "Status"
	let sortDirection = 'desc'; // Default to ascending order

	// Function to toggle sorting
	function sortBy(column: string) {
		if (sortColumn === column) {
			// Toggle direction if already sorted by this column
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			// Sort by the new column, default to ascending
			sortColumn = column;
			sortDirection = 'asc';
		}
	}

	//////////////// Pagination Logic ////////////////
	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;

	$: totalPages = Math.ceil(Math.max((sortedUsers ?? []).length, 1) / itemsPerPage);
	$: paginatedUsers = (sortedUsers ?? []).slice(0, itemsPerPage);

	function updatePagination() {
		const idx = (currentPage - 1) * itemsPerPage;
		paginatedUsers = sortedUsers.slice(idx, Math.min(idx + itemsPerPage, sortedUsers.length));
	}

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		updatePagination();
	}

	// Format tag name to capitalize first letter
	function formatTagName(tag: string): string {
		return tag.charAt(0).toUpperCase() + tag.slice(1);
	}
	import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';

	function getTagsByIds(tagIds: number[], allTags: any[]) {
		return allTags.filter((tag) => tagIds.includes(tag.id));
	}

	$: userMatchedTags = users?.map((user) => ({
		id: user.id,
		tags: getTagsByIds(user.user_tags ?? [], all_tags ?? [])
	}));

	// Generate dummy work shift data for users
	function generateWorkShift(userId: number) {
		const shifts = [
			{
				label: 'Morning Shift',
				schedule: 'Mon-Fri: 9:00 AM - 5:00 PM\nSat-Sun: Off'
			},
			{
				label: 'Evening Shift',
				schedule: 'Mon-Fri: 2:00 PM - 10:00 PM\nSat-Sun: Off'
			},
			{
				label: 'Night Shift',
				schedule: 'Sun-Thu: 10:00 PM - 6:00 AM\nFri-Sat: Off'
			},
			{
				label: 'Weekend Shifts',
				schedule: 'Sat-Sun: 10:00 AM - 6:00 PM\nMon-Fri: Off'
			},
			{
				label: '24/7 Available',
				schedule:
					'Monday: 24 Hours\nTuesday: 24 Hours\nWednesday: 24 Hours\nThursday: 24 Hours\nFriday: 24 Hours\nSaturday: 24 Hours\nSunday: 24 Hours'
			},
			{
				label: 'Rotating Shifts',
				schedule:
					'Week 1: Mon-Fri 9:00 AM - 5:00 PM\nWeek 2: Mon-Fri 2:00 PM - 10:00 PM\nWeek 3: Sun-Thu 10:00 PM - 6:00 AM'
			}
		];

		// Use user ID to consistently assign the same shift to the same user
		return shifts[userId % shifts.length];
	}
</script>

<svelte:head>
	<title>Users</title>
</svelte:head>

<div class="flex h-screen">
	<div class="w-4/4 overflow-y-auto bg-white p-8">
		<Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
			<BreadcrumbItem href="/" home>
				<span class="text-gray-400">{t('home')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem>
				<span class="text-gray-700">{t('users')}</span>
			</BreadcrumbItem>
		</Breadcrumb>

		<div class="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
			<!-- Left Section: Title & Description -->
			<div>
				<h2 class="text-2xl font-bold">{t('users_page_title')}</h2>
				<p class="text-gray-600">{t('users_page_description')}</p>
			</div>
			<!-- Right: Buttons in one row -->
			{#if data.role === 'Admin'}
				<div class="flex flex-nowrap gap-2 overflow-x-auto md:overflow-visible">
					<!-- New User Button -->
					<UserSignUp count={users?.length || 0} />
				</div>
			{/if}
		</div>

		<AdminStatistics {users} />

		<!-- Filters and Search Bar - Improved Layout -->
		<div
			class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between"
		>
			<!-- Left side - Buttons -->
			<div class="flex flex-wrap gap-3">
				<!-- Status Filter -->
				<div>
					<Button
						color={!selectedStatuses.has('All') ? 'dark' : 'none'}
						class={`${!selectedStatuses.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_status')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-44 p-2 shadow-lg">
						{#each statusOptions as status}
							<div class="rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedStatuses.has(status)}
									on:change={() => toggleStatus(status)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{status === 'All'
											? 'All Status'
											: status.charAt(0).toUpperCase() + status.slice(1)}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Role Filter -->
				<div>
					<Button
						color={!selectedRoles.has('All') ? 'dark' : 'none'}
						class={`${!selectedRoles.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_role')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-44 p-2 shadow-lg">
						{#each roleOptions as role}
							<div class="rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedRoles.has(role)}
									on:change={() => toggleRole(role)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">{role}</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Partner Filter -->
				<div>
					<Button
						color={!selectedPartners.has('All') ? 'dark' : 'none'}
						class={`${!selectedPartners.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_partner')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each partnerOptions as partner}
							<div class="flex items-center justify-between rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedPartners.has(partner)}
									on:change={() => togglePartner(partner)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{partner}
										{#if partner !== 'All'}
											({partnerCounts[partner] || 0})
										{/if}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Reset Filter -->
				<Button
					color="none"
					on:click={resetFilters}
					class="w-auto border shadow-md hover:bg-gray-100"
				>
					{t('filter_reset')}
				</Button>
			</div>

			<!-- Right side - Search Bar -->
			<div class="relative w-full shadow-md lg:w-1/3">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="searchBar"
					type="text"
					placeholder={t('search_user_placeholder')}
					bind:value={searchQuery}
					class={`block w-full rounded-lg border bg-white py-2.5 pl-10
                        focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		<!-- Table section -->
		<Table shadow class="w-full table-fixed">
			<TableHead>
				<TableHeadCell class="w-[60px]" on:click={() => sortBy('id')}>
					<div class="flex items-center justify-start">
						{#if sortColumn === 'id' && sortDirection === 'desc'}
							{t('table_no')} <CaretDownSolid class="inline-block h-4 w-4" />
						{:else}
							{t('table_no')} <CaretUpSolid class="inline-block h-4 w-4" />
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell class="w-[90px]" on:click={() => sortBy('status')}>
					<div class="flex items-center justify-start">
						{#if sortColumn === 'status' && sortDirection === 'desc'}
							{t('table_status')} <CaretDownSolid class="inline-block h-4 w-4" />
						{:else}
							{t('table_status')} <CaretUpSolid class="inline-block h-4 w-4" />
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell class="w-[110px]">{t('table_workload')}</TableHeadCell>
				<!-- <TableHeadCell class="w-[150px]">Nickname</TableHeadCell> -->
				<TableHeadCell class="w-[200px]">{t('table_name')}</TableHeadCell>
				<TableHeadCell class="w-[100px]">{t('table_role')}</TableHeadCell>
				<TableHeadCell class="w-[110px]">{t('table_partner')}</TableHeadCell>
				<TableHeadCell class="w-[110px]">{t('table_department')}</TableHeadCell>
				<TableHeadCell class="w-[110px]">{t('table_specialize_tag')}</TableHeadCell>
				<TableHeadCell class="w-[120px]">Work Shift</TableHeadCell>
				<!-- <TableHeadCell class="w-[120px]" on:click={() => sortBy('last_active')}>
                    <div class="flex items-center justify-start">
                        {#if sortColumn === 'last_active' && sortDirection === 'desc'}
                            {t('table_time')} <CaretUpSolid class="inline-block h-4 w-4" />
                        {:else}
                            {t('table_time')} <CaretDownSolid class="inline-block h-4 w-4" />
                        {/if}
                    </div>
                </TableHeadCell> -->
				<TableHeadCell class="w-[150px]">{t('table_last_active')}</TableHeadCell>
			</TableHead>
			<TableBody>
				{#if paginatedUsers.length === 0}
					<TableBodyRow>
						<TableBodyCell colspan={10} class="py-4 text-center text-gray-500">
							No user(s) to display
						</TableBodyCell>
					</TableBodyRow>
				{:else}
					{#each paginatedUsers as user}
						<TableBodyRow>
							<TableBodyCell>
								<a
									href="/users/{user.id}"
									class="flex items-center justify-start py-2 text-blue-600 hover:underline"
								>
									{user.id}<EditSolid class="h-4 w-4" />
								</a>
							</TableBodyCell>
							<TableBodyCell>
								<span
									class={user.status === 'online'
										? 'inline-block rounded-md bg-green-200 px-2 py-1 text-sm text-green-700'
										: user.status === 'busy'
											? 'inline-block rounded-md bg-red-200 px-2 py-1 text-sm text-red-700'
											: user.status === 'away'
												? 'inline-block rounded-md bg-yellow-200 px-2 py-1 text-sm text-yellow-700'
												: user.status === 'offline'
													? 'inline-block rounded-md bg-gray-100 px-2 py-1 text-sm text-gray-700'
													: 'inline-block rounded-md bg-gray-100 px-2 py-1 text-sm text-gray-700'}
								>
									<!-- {user.status.charAt(0).toUpperCase() + user.status.slice(1)} -->
									{t(user.status)}
								</span>
							</TableBodyCell>
							<TableBodyCell>{user.current_workload}</TableBodyCell>
							<!-- <TableBodyCell><span class="break-words">{user.name}</span></TableBodyCell> -->
							<TableBodyCell>
								<span class="break-words">{user.first_name} {user.last_name}</span>
								<div class="truncate text-xs text-gray-500">{user.name}</div>
							</TableBodyCell>
							<TableBodyCell>
								{user.roles ? user.roles : '-'}
							</TableBodyCell>
							<TableBodyCell>
								<!-- Partner badges - updated with tooltips -->
								{#if user.partners && user.partners.length > 0}
									{#if user.partners.length === 1}
										<!-- Show single partner badge -->
										<span
											class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
										>
											<!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(user.partners[0].color)}`}></span> -->
											<Indicator
												size="sm"
												class={`mr-1 ${getColorClass(user.partners[0].color)} inline-block`}
											/>
											{user.partners[0].code}
										</span>
									{:else}
										<!-- Show badge with count -->
										<div class="relative inline-block">
											<span
												class="text-white-700 inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
												data-popover-target="popover-partners-{user.id}"
											>
												<span class="flex items-center gap-1">
													<!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
													<span class="relative flex -space-x-1">
														{#each user.partners.slice(0, 3) as partners, i (partners.name)}
															<!-- <span
                                                                class={`inline-block w-2 h-2 rounded-full ${getColorClass(partners.color)}`}
                                                                style="z-index: {10 - i};"
                                                            ></span> -->
															<Indicator
																size="sm"
																class={`${getColorClass(partners.color)}`}
																style="z-index: {10 - i};"
															/>
														{/each}
													</span>
													{user.partners.length}
													{t('labels')}
												</span>
											</span>

											<!-- Tooltip for all partners -->
											<Tooltip triggeredBy="[data-popover-target='popover-partners-{user.id}']">
												<div class="max-w-xs px-2 py-1">
													<ul class="space-y-1">
														{#each user.partners as partner}
															<li class="flex items-center gap-1">
																<!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
																<!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(partner.color)}`}></span> -->
																<Indicator
																	size="sm"
																	class={`mr-1 ${getColorClass(partner.color)}`}
																/>
																{partner.code}
															</li>
														{/each}
													</ul>
												</div>
											</Tooltip>
										</div>
									{/if}
								{:else}
									-
								{/if}
							</TableBodyCell>
							<TableBodyCell>
								<!-- Department badges - updated with tooltips -->
								{#if user.departments && user.departments.length > 0}
									{#if user.departments.length === 1}
										<!-- Show single department badge -->
										<span
											class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
										>
											<!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(user.departments[0].color)}`}></span> -->
											<Indicator
												size="sm"
												class={`mr-1 ${getColorClass(user.departments[0].color)} inline-block`}
											/>
											{formatTagName(user.departments[0].code)}
										</span>
									{:else}
										<!-- Show badge with count -->
										<div class="relative inline-block">
											<span
												class="text-white-700 inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
												data-popover-target="popover-departments-{user.id}"
											>
												<span class="flex items-center gap-1">
													<!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
													<span class="relative flex -space-x-1">
														{#each user.departments.slice(0, 3) as department, i (department.name)}
															<!-- <span
                                                                class={`inline-block w-2 h-2 rounded-full ${getColorClass(department.color)}`}
                                                                style="z-index: {10 - i};"
                                                            ></span> -->
															<Indicator
																size="sm"
																class={`${getColorClass(department.color)}`}
																style="z-index: {10 - i};"
															/>
														{/each}
													</span>
													{user.departments.length}
													{t('labels')}
												</span>
											</span>

											<!-- Tooltip for all departments -->
											<Tooltip triggeredBy="[data-popover-target='popover-departments-{user.id}']">
												<div class="max-w-xs px-2 py-1">
													<ul class="space-y-1">
														{#each user.departments as department}
															<li class="flex items-center gap-1">
																<!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
																<!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(department.color)}`}></span> -->
																<Indicator
																	size="sm"
																	class={`mr-1 ${getColorClass(department.color)}`}
																/>
																{formatTagName(department.code)}
															</li>
														{/each}
													</ul>
												</div>
											</Tooltip>
										</div>
									{/if}
								{:else}
									-
								{/if}
							</TableBodyCell>
							<TableBodyCell>
								<!-- Specialized Tag Badges -->
								{#if user.user_tags && user.user_tags.length > 0}
									{#each userMatchedTags.filter((ut) => ut.id === user.id) as matched}
										{#if matched.tags.length === 1}
											<span
												class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
											>
												<!-- <span class="inline-block w-2 h-2 rounded-full mr-2" style="background-color: {matched.tags[0].color}"></span> -->
												<!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {matched.tags[0].color}"></span> -->
												<Indicator
													size="sm"
													class={`mr-1 ${getColorClass(matched.tags[0].color)} inline-block`}
												/>
												{matched.tags[0].name}
											</span>
										{:else}
											<div class="relative inline-block">
												<span
													class="inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
													data-popover-target="popover-tags-{user.id}"
												>
													<span class="flex items-center gap-1">
														<!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
														<span class="relative flex -space-x-1">
															{#each matched.tags.slice(0, 3) as tag, i (tag.name)}
																<!-- <span
																	class={`inline-block w-2 h-2 rounded-full ${getColorClass(tag.color)}`}
																	style="z-index: {10 - i};"
																></span> -->
																<Indicator
																	size="sm"
																	class={`${getColorClass(tag.color)}`}
																	style="z-index: {10 - i};"
																/>
															{/each}
														</span>
														{matched.tags.length}
														{t('labels')}
													</span>
												</span>

												<!-- Tooltip for all tags -->
												<Tooltip triggeredBy="[data-popover-target='popover-tags-{user.id}']">
													<div class="max-w-xs px-2 py-1">
														<ul class="space-y-1">
															{#each matched.tags as tag}
																<li class="flex items-center gap-1">
																	<!-- <span class="inline-block w-2 h-2 rounded-full"style="background-color: {tag.color}"></span> -->
																	<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)}`} />
																	{tag.name}
																</li>
															{/each}
														</ul>
													</div>
												</Tooltip>
											</div>
										{/if}
									{/each}
								{:else}
									-
								{/if}
							</TableBodyCell>
							<TableBodyCell>
								<!-- Work Shift Badge with Tooltip -->
								{@const workShift = generateWorkShift(user.id)}
								<div class="relative inline-block">
									<span
										class="inline-flex cursor-pointer items-center gap-1 rounded-md bg-blue-50 px-2 py-1 text-sm text-blue-700"
										data-popover-target="popover-workshift-{user.id}"
									>
										<ClockSolid class="h-3 w-3" />
										{workShift.label}
									</span>

									<!-- Tooltip for work shift schedule -->
									<Tooltip triggeredBy="[data-popover-target='popover-workshift-{user.id}']">
										<div class="max-w-xs px-2 py-1">
											<div class="mb-1 font-semibold text-gray-900">Weekly Schedule</div>
											<div class="whitespace-pre-line text-sm text-gray-700">
												{workShift.schedule}
											</div>
										</div>
									</Tooltip>
								</div>
							</TableBodyCell>
							<!-- <TableBodyCell>{timeAgo(user.last_active)}</TableBodyCell> -->
							<TableBodyCell>
								<div>{displayDate(user.last_active).date}</div>
								<div>{displayDate(user.last_active).time}</div>
							</TableBodyCell>
						</TableBodyRow>
					{/each}
				{/if}
			</TableBody>
		</Table>

		<!-- Pagination Layout -->
		<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
	</div>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>
